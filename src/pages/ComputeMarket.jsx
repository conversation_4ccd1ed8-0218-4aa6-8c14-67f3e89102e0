import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Input,
  Button,
  Select,
  Tag,
  Space,
  Typography,
  Divider,
  Spin,
  Empty,
  Badge,
  Tooltip,
  Rate,
  Avatar,
  Segmented,
  Drawer,
  Modal,
} from 'antd';
import { useRequest } from 'ahooks';

import { formatCurrency } from '@/lib/utils';
import { appTemplateService } from '@/services/appTemplateService';
import AIRecommendationDialog from '@/components/AIRecommendationDialog';
import InstanceOrderWizard from '@/components/InstanceOrderWizard';
import { useInstances } from '@/contexts/InstanceContext';
import { toast } from 'sonner';
import { getGpuList, getCpuList, getRegionList } from '@/services/enums';
import { getMarketList } from '@/services/market';
import PageHeader from '@/components/PageHeader';
import { RainbowButton } from '@/components/magicui/rainbow-button';
import { ShimmerButton } from '@/components/magicui/shimmer-button';

import {
  Search,
  Filter,
  Cpu,
  HardDrive,
  Zap,
  MapPin,
  Clock,
  Star,
  ShoppingCart,
  Sparkles,
  Server,
  Bot,
  RotateCcw,
  Package,
  Layers,
} from 'lucide-react';

const { Title, Text } = Typography;

const ComputeMarket = () => {
  const { addInstance } = useInstances();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState('');
  const [selectedGpuModel, setSelectedGpuModel] = useState('all');
  const [selectedCpuModel, setSelectedCpuModel] = useState('all');
  const [showAIDialog, setShowAIDialog] = useState(false);
  const [showOrderWizard, setShowOrderWizard] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);
  const [appTemplates, setAppTemplates] = useState([]);
  const [appTemplatesLoading, setAppTemplatesLoading] = useState(false);

  // 分页参数
  const [pagination, setPagination] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  // 获取 GPU 列表
  const { data: gpuList = [], loading: gpuLoading } = useRequest(getGpuList, {
    onError: error => {
      console.error('获取 GPU 列表失败:', error);
    },
  });

  // 获取 CPU 列表
  const { data: cpuList = [], loading: cpuLoading } = useRequest(getCpuList, {
    onError: error => {
      console.error('获取 CPU 列表失败:', error);
    },
  });

  // 获取区域列表
  const { data: regionList = [], loading: regionLoading } = useRequest(
    getRegionList,
    {
      onError: error => {
        console.error('获取区域列表失败:', error);
      },
    }
  );

  // 构建查询参数
  const buildQueryParams = useMemo(() => {
    const params = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
    };

    // 添加搜索条件
    if (searchTerm) {
      params.machineName = searchTerm;
    }

    // 添加设备类型
    if (selectedType !== 'all') {
      params.machineType = selectedType;
    }

    // 添加区域筛选
    if (selectedLocation !== 'all') {
      const region = regionList.find(r => r.regionName === selectedLocation);
      if (region) {
        params.regionCode = region.id;
      }
    }

    // 添加 GPU 型号筛选
    if (selectedGpuModel !== 'all') {
      const gpu = gpuList.find(g => g.gpuName === selectedGpuModel);
      if (gpu) {
        params.code = gpu.gpuCode;
        if (selectedType === 'GPU') {
          params.minGpuCode = gpu.gpuCode;
        }
      }
    }

    // 添加 CPU 型号筛选
    if (selectedCpuModel !== 'all') {
      const cpu = cpuList.find(c => c.cpuName === selectedCpuModel);
      if (cpu) {
        params.code = cpu.cpuCode;
      }
    }

    return params;
  }, [
    pagination,
    searchTerm,
    selectedType,
    selectedLocation,
    selectedGpuModel,
    selectedCpuModel,
    regionList,
    gpuList,
    cpuList,
  ]);

  useEffect(() => {
    console.log(buildQueryParams);
  }, [buildQueryParams]);

  // 获取算力市场列表
  const {
    data: marketData,
    loading: marketLoading,
    refresh: refreshMarket,
  } = useRequest(() => getMarketList(buildQueryParams), {
    refreshDeps: [JSON.stringify(buildQueryParams)],
    retryCount: 3,
    retryInterval: 1000,
  });

  // 加载应用模板列表
  const loadAppTemplates = async () => {
    setAppTemplatesLoading(true);
    try {
      const data = await appTemplateService.getAppTemplates({
        pageIndex: 1,
        pageSize: 100,
        status: 1, // 只获取启用的应用
      });

      setAppTemplates(data.records || []);
    } catch (error) {
      console.error('加载应用模板失败:', error);
    } finally {
      setAppTemplatesLoading(false);
    }
  };

  // 处理从应用市场跳转过来的应用参数
  useEffect(() => {
    const state = location.state;
    if (state?.selectedApplication) {
      setSelectedApplication(state.selectedApplication.id);
    }

    const appId = searchParams.get('app');
    if (appId) {
      setSelectedApplication(appId);
    }
  }, [searchParams, location.state]);

  // 加载应用模板
  useEffect(() => {
    loadAppTemplates();
  }, []);

  // 获取可用设备列表（从接口数据转换）
  const availableDevices = useMemo(() => {
    if (!marketData?.records) return [];

    return marketData.records.map(machine => ({
      id: machine.id,
      name: machine.machineName,
      alias: machine.machineAlias,
      type: machine.machineType,
      cpu: machine.cpuCode,
      cpuNum: machine.cpuNum,
      memory: `${machine.memorySize}GB`,
      gpu: machine.gpuCode,
      gpuNum: machine.gpuNum,
      storage: `${machine.diskSize}GB ${machine.diskType}`,
      location: machine.regionCode,
      ip: machine.innerIp,
      status: machine.healthStatus === 1 ? 'online' : 'offline',
      provider: `用户${machine.userId}`,
      rating: 4.8, // 默认评分
      totalOrders: 156, // 默认订单数
      availability: {
        start: '00:00',
        end: '23:59',
      },
      resourcePricings: machine.resourcePricings || [],
      // 计算最低价格（按需价格）
      pricePerHour:
        machine.resourcePricings?.find(
          p => p.resourceType === 'GPU' && p.pricingModel === 'ON_DEMAND'
        )?.pricePerUnit || 0,
      computeCapability: machine.computeCapability,
      description: machine.description || '',
      exclusive: false, // 从接口获取的都是可租用的
    }));
  }, [marketData]);

  // 由于筛选已经在接口层面完成，这里主要处理应用要求的筛选
  const filteredDevices = useMemo(() => {
    if (!availableDevices.length) return [];

    return availableDevices.filter(device => {
      // 检查是否满足选中应用的硬件要求
      const matchesApplicationRequirements =
        !selectedApplication ||
        (() => {
          const app = appTemplates.find(a => a.id === selectedApplication);
          if (!app) return true;

          // 检查CPU核心数
          const cpuMatch = device.cpuNum >= (app.minCpuCores || 0);

          // 检查内存
          const deviceMemoryGB = parseInt(
            device.memory.match(/\d+/)?.[0] || '0'
          );
          const memoryMatch = deviceMemoryGB >= (app.minMemoryGb || 0);

          // 检查GPU要求
          const gpuMatch = device.gpuNum >= (app.minGpuCount || 0);

          return cpuMatch && memoryMatch && gpuMatch;
        })();

      return matchesApplicationRequirements;
    });
  }, [availableDevices, selectedApplication, appTemplates]);

  // 重置筛选条件
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedLocation('all');
    setSelectedApplication('');
    setSelectedGpuModel('all');
    setSelectedCpuModel('all');
  };

  const handleRentDevice = device => {
    setSelectedDevice(device);
    setShowOrderWizard(true);
  };

  const handleConfirmOrder = async orderConfig => {
    try {
      const selectedApp = orderConfig.application;
      const device = orderConfig.device;

      // 创建实例数据
      const instanceData = {
        name: orderConfig.instanceName,
        deviceId: device.id,
        status: orderConfig.deploymentId ? 'deploying' : 'running', // 如果有部署ID，状态为部署中
        cpu: device.cpu || device.cpuCode,
        memory: device.memory || `${device.memorySize}GB`,
        gpu: device.gpu || device.gpuCode,
        storage: device.storage || `${device.diskSize}GB`,
        pricePerHour: orderConfig.pricePerHour,
        totalCost: orderConfig.totalCost,
        startTime: new Date().toISOString(),
        userId: '1',
        applications: selectedApp ? [selectedApp.id] : [],
        applicationInfo: selectedApp
          ? {
            id: selectedApp.id,
            name: selectedApp.name,
            icon: selectedApp.iconUrl,
            version: selectedApp.version,
            status: orderConfig.deploymentId ? 'deploying' : 'running',
            ports: selectedApp.protocolPortsList?.map(p => p.port) || [8080],
            accessUrl: orderConfig.deploymentId
              ? null // 部署中时暂无访问地址
              : `http://${device.innerIp || device.ip}:${selectedApp.protocolPortsList?.[0]?.port || 8080
              }`,
            pricePerHour: selectedApp.pricePerHour || 0,
            installTime: new Date().toISOString(),
            config: orderConfig.appConfig,
            envVars: orderConfig.envVars,
            deploymentId: orderConfig.deploymentId, // 保存部署ID用于后续查询状态
          }
          : undefined,
        sshInfo: {
          host: device.innerIp || device.ip,
          port: 22000 + parseInt(device.id),
          username: 'user1',
        },
        // 保存订购配置信息
        orderConfig: {
          duration: orderConfig.duration,
          gpuCount: orderConfig.gpuCount,
          extraDiskSize: orderConfig.extraDiskSize,
          autoRenew: orderConfig.autoRenew,
        },
      };

      // 先添加实例到状态中
      const newInstance = await addInstance(instanceData);

      // 关闭对话框
      setShowOrderWizard(false);

      // 显示成功消息
      if (orderConfig.deploymentId) {
        toast.success('实例创建成功，应用正在部署中，请稍候...');
      } else {
        toast.success('实例创建成功！');
      }

      // 跳转到我的实例页面
      navigate(`/my-instances?id=${newInstance.id}`);
    } catch (error) {
      console.error('订购失败:', error);
    }
  };

  return (
    <div className="min-h-screen">
      {/* 页面标题和AI推荐按钮 */}
      <div className="mb-6">

        <PageHeader
          title="算力市场 "
          subtitle="发现和租用最适合您需求的计算资源"
        />

      </div>

      {/* 主要内容区域：左侧筛选，右侧资源列表 */}
      <Row gutter={[24, 24]}>
        {/* 左侧筛选面板 */}
        <Col xs={24} lg={6}>
          <Card
            className="sticky top-6 rounded-lg border-none shadow-sm transition-shadow hover:shadow-md"
            title={
              <Space>
                <Filter className="w-5 h-5" />
                筛选条件
              </Space>
            }
            extra={
              <Button
                type="text"
                size="small"
                icon={<RotateCcw className="w-4 h-4" />}
                onClick={resetFilters}
              >
                重置
              </Button>
            }
          >
            <Space direction="vertical" size="large" className="w-full">
              {/* 应用场景选择区域 */}
              <div>
                <Title level={5} className="!mb-3">
                  <Space>
                    <Sparkles className="w-4 h-4 text-blue-500" />
                    应用场景
                  </Space>
                </Title>

                {selectedApplication ? (
                  <Card size="small" className="mb-3">
                    <div className="flex items-center justify-between">
                      <Space>
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          {appTemplates.find(
                            app => app.id === selectedApplication
                          )?.iconUrl ? (
                            <img
                              src={
                                appTemplates.find(
                                  app => app.id === selectedApplication
                                )?.iconUrl
                              }
                              alt="app icon"
                              className="w-6 h-6 rounded"
                            />
                          ) : (
                            <Package className="w-4 h-4 text-blue-600" />
                          )}
                        </div>
                        <div>
                          <Text strong>
                            {
                              appTemplates.find(
                                app => app.id === selectedApplication
                              )?.name
                            }
                          </Text>
                          <div>
                            <Text type="secondary" className="text-xs">
                              v
                              {
                                appTemplates.find(
                                  app => app.id === selectedApplication
                                )?.version
                              }
                            </Text>
                          </div>
                        </div>
                      </Space>
                      <Button
                        type="text"
                        size="small"
                        onClick={() => setSelectedApplication('')}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        ×
                      </Button>
                    </div>
                    <Divider className="!my-2" />

                    {/* 应用要求 */}
                    <div>
                      <Text type="secondary" className="text-xs">
                        硬件要求:
                      </Text>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {(() => {
                          const app = appTemplates.find(
                            a => a.id === selectedApplication
                          );
                          if (!app) return null;

                          return (
                            <>
                              {app.minCpuCores > 0 && (
                                <Tag size="small" color="blue">
                                  {app.minCpuCores}核+
                                </Tag>
                              )}
                              {app.minMemoryGb > 0 && (
                                <Tag size="small" color="green">
                                  {app.minMemoryGb}GB+
                                </Tag>
                              )}
                              {app.minGpuCount > 0 && (
                                <Tag size="small" color="orange">
                                  {app.minGpuCount}GPU+
                                </Tag>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </div>

                    <Button
                      type="primary"
                      size="small"
                      block
                      onClick={() => setShowApplicationDialog(true)}
                      className="mt-3"
                    >
                      更换应用
                    </Button>
                  </Card>
                ) : (
                  <Button
                    type="dashed"
                    size="middle"
                    block
                    onClick={() => setShowApplicationDialog(true)}
                    icon={<Sparkles className="w-4 h-4" />}
                  >
                    选择应用
                  </Button>
                )}
              </div>

              {/* 搜索框 */}
              <div>
                <Title level={5} className="!mb-3">
                  搜索
                </Title>
                <Input.Search
                  placeholder="搜索设备、CPU、GPU..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  allowClear
                />
              </div>

              {/* 设备类型筛选 */}
              <div>
                <Title level={5} className="!mb-3">
                  设备类型
                </Title>
                <Segmented
                  options={[
                    {
                      value: 'all',
                      label: '全部',
                      icon: <Layers className="!w-4 !h-4" />,
                    },
                    {
                      value: 'GPU',
                      label: 'GPU',
                      icon: <Zap className="!w-4 !h-4" />,
                    },
                    {
                      value: 'CPU',
                      label: 'CPU',
                      icon: <Cpu className="!w-4 !h-4" />,
                    },
                  ]}
                  value={selectedType}
                  onChange={value => {
                    setSelectedType(value);
                    // 切换设备类型时，重置相应的型号筛选
                    if (value === 'CPU') {
                      // 切换到 CPU 时，重置 GPU 筛选
                      setSelectedGpuModel('all');
                    } else if (value === 'GPU') {
                      // 切换到 GPU 时，重置 CPU 筛选
                      setSelectedCpuModel('all');
                    }
                    // 如果选择全部，保持当前筛选不变
                  }}
                  block
                />
              </div>

              {/* 地理位置筛选 */}
              <div>
                <Title level={5} className="!mb-3">
                  地理位置
                </Title>
                <div className="flex flex-wrap gap-2">
                  <Button
                    type={selectedLocation === 'all' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => setSelectedLocation('all')}
                  >
                    全部地区
                  </Button>
                  {regionLoading ? (
                    <Spin size="small" />
                  ) : (
                    regionList.map(region => (
                      <Button
                        key={region.id}
                        type={
                          selectedLocation === region.regionName
                            ? 'primary'
                            : 'default'
                        }
                        size="small"
                        onClick={() => setSelectedLocation(region.regionName)}
                      >
                        {region.regionName}
                      </Button>
                    ))
                  )}
                </div>
              </div>

              {/* GPU型号筛选 - 只在设备类型为 GPU 或全部时显示 */}
              {(selectedType === 'all' || selectedType === 'GPU') && (
                <div>
                  <Title level={5} className="!mb-3">
                    GPU型号
                  </Title>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      type={selectedGpuModel === 'all' ? 'primary' : 'default'}
                      size="small"
                      onClick={() => {
                        setSelectedGpuModel('all');
                      }}
                    >
                      全部型号
                    </Button>
                    {gpuLoading ? (
                      <Spin size="small" />
                    ) : (
                      gpuList.map(gpu => (
                        <Button
                          key={gpu.id}
                          type={
                            selectedGpuModel === gpu.gpuName
                              ? 'primary'
                              : 'default'
                          }
                          size="small"
                          onClick={() => {
                            setSelectedGpuModel(gpu.gpuName);
                            // 选择了具体 GPU 型号，取消 CPU 选择
                            setSelectedCpuModel('all');
                          }}
                        >
                          {gpu.gpuName}
                        </Button>
                      ))
                    )}
                  </div>
                </div>
              )}

              {/* CPU型号筛选 - 只在设备类型为 CPU 或全部时显示 */}
              {(selectedType === 'all' || selectedType === 'CPU') && (
                <div>
                  <Title level={5} className="!mb-3">
                    CPU型号
                  </Title>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      type={selectedCpuModel === 'all' ? 'primary' : 'default'}
                      size="small"
                      onClick={() => {
                        setSelectedCpuModel('all');
                      }}
                    >
                      全部型号
                    </Button>
                    {cpuLoading ? (
                      <Spin size="small" />
                    ) : (
                      cpuList.map(cpu => (
                        <Button
                          key={cpu.id}
                          type={
                            selectedCpuModel === cpu.cpuName
                              ? 'primary'
                              : 'default'
                          }
                          size="small"
                          onClick={() => {
                            setSelectedCpuModel(cpu.cpuName);
                            // 选择了具体 CPU 型号，取消 GPU 选择
                            setSelectedGpuModel('all');
                          }}
                        >
                          {cpu.cpuName}
                        </Button>
                      ))
                    )}
                  </div>
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* 右侧资源列表 */}
        <Col xs={24} lg={18}>
          <Card
            className="rounded-lg border-none"
            title={
              <Space>
                <Server className="w-5 h-5" />
                可用算力资源
              </Space>
            }
            extra={
              <RainbowButton
                className="rounded-lg bg-primary"
                onClick={() => setShowAIDialog(true)}
              >
                <Sparkles className="w-4 h-4" />
                AI智能推荐
              </RainbowButton>
            }
          >
            {marketLoading ? (
              <div className="text-center py-12">
                <Spin size="large" />
                <div className="mt-4">
                  <Title level={4}>正在加载算力资源...</Title>
                  <Text type="secondary">请稍候</Text>
                </div>
              </div>
            ) : filteredDevices.length === 0 ? (
              <Empty
                image={<Server className="w-16 h-16 text-gray-300 mx-auto" />}
                description={
                  <div>
                    <Title level={4}>暂无匹配的设备</Title>
                    <Text type="secondary">请尝试调整筛选条件</Text>
                  </div>
                }
              />
            ) : (
              <Row gutter={[16, 16]}>
                {filteredDevices.map(device => (
                  <Col xs={24} xl={12} key={device.id}>
                    <ComputeDeviceCard
                      device={device}
                      onRent={handleRentDevice}
                    />
                  </Col>
                ))}
              </Row>
            )}
          </Card>
        </Col>
      </Row>

      {/* 应用选择对话框 */}
      <Modal
        title="选择应用场景"
        open={showApplicationDialog}
        onCancel={() => setShowApplicationDialog(false)}
        footer={null}
        width={800}
      >
        <Spin spinning={appTemplatesLoading}>
          <Row gutter={[16, 16]}>
            {appTemplates.map(app => (
              <Col xs={12} sm={8} md={6} key={app.id}>
                <Card
                  hoverable
                  className="text-center cursor-pointer"
                  onClick={() => {
                    setSelectedApplication(app.id);
                    setShowApplicationDialog(false);
                  }}
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    {app.iconUrl ? (
                      <img
                        src={app.iconUrl}
                        alt={app.name}
                        className="w-8 h-8 rounded"
                      />
                    ) : (
                      <Package className="w-6 h-6 text-blue-600" />
                    )}
                  </div>
                  <Title level={5} ellipsis={{ tooltip: app.name }}>
                    {app.name}
                  </Title>
                  <Text type="secondary" className="text-xs">
                    v{app.version}
                  </Text>
                  <div className="mt-2">
                    <Text type="secondary" className="text-xs">
                      硬件要求:
                    </Text>
                    <div className="flex flex-wrap gap-1 justify-center mt-1">
                      {app.minCpuCores > 0 && (
                        <Tag size="small">{app.minCpuCores}核+</Tag>
                      )}
                      {app.minMemoryGb > 0 && (
                        <Tag size="small">{app.minMemoryGb}GB+</Tag>
                      )}
                      {app.minGpuCount > 0 && (
                        <Tag size="small">{app.minGpuCount}GPU+</Tag>
                      )}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Spin>
      </Modal>

      {/* AI推荐对话框 */}
      <AIRecommendationDialog
        isOpen={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onRentDevice={handleRentDevice}
      />

      {/* 实例订购向导 */}
      <InstanceOrderWizard
        visible={showOrderWizard}
        onCancel={() => setShowOrderWizard(false)}
        device={selectedDevice}
        application={
          selectedApplication
            ? appTemplates.find(app => app.id === selectedApplication)
            : null
        }
        title="订购算力实例"
        onConfirm={handleConfirmOrder}
      />
    </div>
  );
};

// 算力设备卡片组件
const ComputeDeviceCard = ({ device, onRent, isRecommended = false }) => {
  return (
    <Card
      hoverable
      className={`${isRecommended ? 'border-purple-300 shadow-lg' : ''} border hover:shadow-lg`}
      actions={[
        <div className="px-4">
          <Button
            type="primary"
            size="large"
            block
            onClick={() => onRent(device)}
            icon={<ShoppingCart className="w-4 h-4" />}
          >
            立即租用
          </Button>
        </div>,
      ]}
    >
      {/* 卡片头部 */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <Title level={4} className="!mb-2">
            {device.name}
          </Title>
          <Space size="middle">
            <Space size="small">
              <MapPin className="w-4 h-4 text-gray-400" />
              <Text type="secondary">{device.location}</Text>
            </Space>
            <Space size="small">
              <Rate
                disabled
                defaultValue={device.rating}
                count={5}
                className="text-xs"
              />
              <Text type="secondary">({device.totalOrders}单)</Text>
            </Space>
          </Space>
          <div className="mt-1">
            <Text type="secondary" className="text-xs">
              提供商: {device.provider}
            </Text>
          </div>
        </div>
        <Tag color={device.type === 'GPU' ? 'blue' : 'green'}>
          {device.type}
        </Tag>
      </div>

      {/* 设备规格 */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col span={12}>
          <Space>
            <Cpu className="w-4 h-4 text-gray-400" />
            <div>
              <Text type="secondary" className="text-xs">
                CPU
              </Text>
              <div className="font-medium">{device.cpu}</div>
            </div>
          </Space>
        </Col>
        <Col span={12}>
          <Space>
            <HardDrive className="w-4 h-4 text-gray-400" />
            <div>
              <Text type="secondary" className="text-xs">
                内存
              </Text>
              <div className="font-medium">{device.memory}</div>
            </div>
          </Space>
        </Col>
        <Col span={12}>
          <Space>
            <Zap className="w-4 h-4 text-gray-400" />
            <div>
              <Text type="secondary" className="text-xs">
                GPU
              </Text>
              <div className="font-medium">{device.gpu}</div>
            </div>
          </Space>
        </Col>
        <Col span={12}>
          <Space>
            <Server className="w-4 h-4 text-gray-400" />
            <div>
              <Text type="secondary" className="text-xs">
                存储
              </Text>
              <div className="font-medium">{device.storage}</div>
            </div>
          </Space>
        </Col>
      </Row>

      {/* 价格和可用时间 */}
      <Divider className="!my-3" />
      <div className="flex justify-between items-center mb-2">
        <Text type="secondary">价格</Text>
        <Text className="text-lg font-bold text-blue-600">
          {formatCurrency(device.pricePerHour)}/小时
        </Text>
      </div>
      <Space size="small" className="text-xs">
        <Clock className="w-4 h-4 text-gray-400" />
        <Text type="secondary">
          可用时间: {device.availability.start} - {device.availability.end}
        </Text>
      </Space>
    </Card>
  );
};

export default ComputeMarket;
