import { ShimmerButton } from '@/components/magicui/shimmer-button';

export default function ShimmerButtonTest() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center gap-8 p-8">
      <div className="space-y-8">
        <h1 className="text-white text-2xl font-bold text-center">
          ShimmerButton 测试
        </h1>
        
        {/* 默认样式 */}
        <div className="space-y-4">
          <h2 className="text-white text-lg">默认样式</h2>
          <ShimmerButton>
            点击我
          </ShimmerButton>
        </div>

        {/* 自定义颜色 */}
        <div className="space-y-4">
          <h2 className="text-white text-lg">自定义颜色</h2>
          <ShimmerButton 
            shimmerColor="#00ff88"
            background="rgba(0, 100, 200, 1)"
          >
            绿色闪光
          </ShimmerButton>
        </div>

        {/* 自定义动画速度 */}
        <div className="space-y-4">
          <h2 className="text-white text-lg">快速动画</h2>
          <ShimmerButton 
            shimmerDuration="1s"
            shimmerColor="#ff6b6b"
          >
            快速闪光
          </ShimmerButton>
        </div>

        {/* 自定义大小和圆角 */}
        <div className="space-y-4">
          <h2 className="text-white text-lg">自定义样式</h2>
          <ShimmerButton 
            borderRadius="8px"
            className="px-8 py-4 text-lg"
            shimmerColor="#ffd700"
          >
            大按钮
          </ShimmerButton>
        </div>
      </div>
    </div>
  );
}
